#include "EffectsManager.h"
#include "SegmentsManager.h"
#include "ScenesManager.h"
#include "PrinterManager.h"
#include "Config.h"

/**
 * 构造函数
 */
EffectsManager::EffectsManager() : segmentsManager(nullptr), scenesManager(nullptr), printerManager(nullptr), leds(nullptr),
                                   ledsInitialized(false), effectRunning(false),
                                   previewMode(false), previewPresetId(""), lastPreviewTime(0) {
    Serial.println("EffectsManager 初始化");
}

/**
 * 析构函数
 */
EffectsManager::~EffectsManager() {
    if (leds) {
        delete[] leds;
    }
}

/**
 * 初始化灯效管理器
 */
void EffectsManager::begin() {
    Serial.println("EffectsManager 初始化完成");

    initializeLEDs();
    loadFromFile();
}

/**
 * 主循环处理函数
 */
void EffectsManager::loop() {
    if (previewMode) {
        // 预览模式下持续渲染预览灯效
        renderPreviewEffect();
    } else if (effectRunning && !currentPresetId.isEmpty()) {
        // 正常模式下渲染当前灯效
        renderCurrentEffect();
    }
}

/**
 * 初始化LED控制
 */
void EffectsManager::initializeLEDs() {
    if (!ledsInitialized) {
        leds = new CRGB[LED_COUNT];
        FastLED.addLeds<WS2812B, LED_PIN, LED_COLOR_ORDER>(leds, LED_COUNT);
        FastLED.setBrightness(255); // 使用参数中的亮度控制
        FastLED.clear();
        FastLED.show();
        ledsInitialized = true;
    }
}

/**
 * 设置SegmentsManager引用
 */
void EffectsManager::setSegmentsManager(SegmentsManager* segmentsManager) {
    this->segmentsManager = segmentsManager;
}

/**
 * 设置ScenesManager引用
 */
void EffectsManager::setScenesManager(ScenesManager* scenesManager) {
    this->scenesManager = scenesManager;
}

/**
 * 设置PrinterManager引用
 */
void EffectsManager::setPrinterManager(PrinterManager* printerManager) {
    this->printerManager = printerManager;
}

/**
 * 注册API端点到WebServer
 */
void EffectsManager::registerAPI(AsyncWebServer& server) {
    Serial.println("EffectsManager API端点已注册");

    // 注意：更具体的路由必须先注册，避免被通用路由拦截

    // 预览模式控制 - 必须在通用路由之前注册
    server.on("/api/effects/preview/enable", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleEnablePreview(request);
    });

    server.on("/api/effects/preview/disable", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleDisablePreview(request);
    });

    server.on("/api/effects/preview/status", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetPreviewStatus(request);
    });

    // 预览功能
    server.on("/api/effects/preview", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // POST请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handlePreviewEffect(request, data, len, index, total);
        }
    );

    // 停止预览
    server.on("/api/effects/preview", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleStopPreview(request);
    });

    // 获取可用灯效类型
    server.on("/api/effects/available", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetAvailableEffects(request);
    });

    // 获取预设统计信息
    server.on("/api/effects/stats", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetStats(request);
    });

    // 停止灯效
    server.on("/api/effects/stop", HTTP_POST, [this](AsyncWebServerRequest* request) {
        this->handleStopEffect(request);
    });

    // 执行预设
    server.on("/api/effects/execute", HTTP_POST, [this](AsyncWebServerRequest* request) {
        // 从查询参数获取预设ID
        if (request->hasParam("id")) {
            String presetId = request->getParam("id")->value();

            if (executePreset(presetId)) {
                sendJsonResponse(request, HTTP_OK, "预设执行成功");
            } else {
                sendErrorResponse(request, HTTP_BAD_REQUEST, "预设执行失败");
            }
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "缺少预设ID参数");
        }
    });

    // 获取所有预设
    server.on("/api/effects", HTTP_GET, [this](AsyncWebServerRequest* request) {
        this->handleGetPresets(request);
    });

    // 创建新预设
    server.on("/api/effects", HTTP_POST,
        [this](AsyncWebServerRequest* request) {
            // POST请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleCreatePreset(request, data, len, index, total);
        }
    );

    // 更新预设
    server.on("/api/effects/*", HTTP_PUT,
        [this](AsyncWebServerRequest* request) {
            // PUT请求的响应在body处理函数中处理
        },
        nullptr,
        [this](AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
            this->handleUpdatePreset(request, data, len, index, total);
        }
    );

    // 删除预设
    server.on("/api/effects/*", HTTP_DELETE, [this](AsyncWebServerRequest* request) {
        this->handleDeletePreset(request);
    });

}

/**
 * 获取所有灯效预设
 */
const std::vector<EffectPreset>& EffectsManager::getPresets() const {
    return presets;
}

/**
 * 根据ID获取灯效预设
 */
const EffectPreset* EffectsManager::getPresetById(const String& id) const {
    for (const auto& preset : presets) {
        if (preset.id == id) {
            return &preset;
        }
    }
    return nullptr;
}

/**
 * 添加新灯效预设
 */
bool EffectsManager::addPreset(const EffectPreset& preset) {
    // 验证预设
    if (!validatePreset(preset)) {
        return false;
    }

    // 检查ID是否已存在
    if (getPresetById(preset.id) != nullptr) {
        return false;
    }
    
    // 添加预设
    presets.push_back(preset);
    
    // 保存到文件
    if (!saveToFile()) {
        presets.pop_back(); // 回滚
        return false;
    }
    
    Serial.printf("[Effects] 添加成功: %s (ID: %s) - 类型: %s\n",
                 preset.name.c_str(), preset.id.c_str(),
                 LightEffect::getEffectName(preset.effectType));
    return true;
}

/**
 * 更新灯效预设
 */
bool EffectsManager::updatePreset(const String& id, const EffectPreset& preset) {
    // 验证预设
    if (!validatePreset(preset, id)) {
        return false;
    }
    
    // 查找要更新的预设
    for (auto& existingPreset : presets) {
        if (existingPreset.id == id) {
            // 保存旧数据用于回滚
            EffectPreset oldPreset = existingPreset;
            
            // 更新预设（保持原有的创建时间）
            existingPreset = preset;
            existingPreset.id = id; // 确保ID不变
            if (existingPreset.createdTime == 0) {
                existingPreset.createdTime = oldPreset.createdTime;
            }
            
            // 保存到文件
            if (!saveToFile()) {
                existingPreset = oldPreset; // 回滚
                return false;
            }
            
            Serial.printf("[Effects] 更新成功: %s (ID: %s) - 类型: %s\n",
                         preset.name.c_str(), id.c_str(),
                         LightEffect::getEffectName(preset.effectType));
            return true;
        }
    }

    return false;
}

/**
 * 删除灯效预设
 */
bool EffectsManager::deletePreset(const String& id) {
    // 如果正在执行该预设，先停止
    if (currentPresetId == id) {
        stopCurrentEffect();
    }
    
    // 查找并删除预设
    for (auto it = presets.begin(); it != presets.end(); ++it) {
        if (it->id == id) {
            String presetName = it->name;
            String presetId = it->id;
            presets.erase(it);

            // 保存到文件
            if (!saveToFile()) {
                return false;
            }

            Serial.printf("[Effects] 删除成功: %s (ID: %s)\n", presetName.c_str(), presetId.c_str());
            return true;
        }
    }

    return false;
}

/**
 * 执行指定的灯效预设
 */
bool EffectsManager::executePreset(const String& presetId) {
    const EffectPreset* preset = getPresetById(presetId);
    if (!preset) {
        return false;
    }

    if (!preset->enabled) {
        return false;
    }

    // 检查LED是否初始化
    if (!ledsInitialized || !leds) {
        return false;
    }

    // 停止当前灯效
    stopCurrentEffect();

    // 设置新的当前预设
    currentPresetId = presetId;
    effectRunning = true;

    return true;
}

/**
 * 停止当前正在执行的灯效
 */
void EffectsManager::stopCurrentEffect() {
    if (effectRunning) {
        effectRunning = false;
        currentPresetId = "";

        // 关闭所有LED
        if (ledsInitialized && leds) {
            fill_solid(leds, LED_COUNT, CRGB::Black);
            delayMicroseconds(100);
            FastLED.show();
            FastLED.show(); // 再次调用确保LED完全更新
        }

    }
}

/**
 * 获取当前正在执行的灯效预设ID
 */
String EffectsManager::getCurrentPresetId() const {
    return currentPresetId;
}

/**
 * 检查灯效是否正在运行
 */
bool EffectsManager::isEffectRunning() const {
    return effectRunning;
}



/**
 * 生成唯一的预设ID
 */
String EffectsManager::generatePresetId() const {
    // 生成格式: effects_时间戳_随机数
    unsigned long timestamp = millis();
    int randomNum = random(1000, 9999);
    return "effects_" + String(timestamp) + "_" + String(randomNum);
}

/**
 * 验证预设配置是否有效
 */
bool EffectsManager::validatePreset(const EffectPreset& preset, const String& excludeId) const {
    // 检查基本字段
    if (preset.name.isEmpty()) {
        return false;
    }

    if (preset.id.isEmpty()) {
        return false;
    }
    
    // 检查名称是否重复
    for (const auto& existingPreset : presets) {
        if (existingPreset.id != excludeId && existingPreset.name == preset.name) {
            return false;
        }
    }
    
    // 检查分段配置
    if (preset.segments.empty()) {
        return false;
    }
    
    // 验证分段是否存在
    if (segmentsManager) {
        for (const auto& segment : preset.segments) {
            if (!segmentsManager->getSegmentById(segment.segmentId)) {
                return false;
            }
        }
    }
    
    // 验证灯效参数
    if (!LightEffect::validateParams(preset.effectType, preset.params)) {
        return false;
    }
    
    return true;
}

/**
 * 渲染当前灯效
 */
void EffectsManager::renderCurrentEffect() {
    if (!ledsInitialized || !leds || currentPresetId.isEmpty()) {
        return;
    }

    const EffectPreset* preset = getPresetById(currentPresetId);
    if (!preset) {
        stopCurrentEffect();
        return;
    }

    unsigned long currentTime = millis();

    // 清空LED数组
    FastLED.clear();

    // 为每个分段渲染灯效
    for (const auto& segmentDir : preset->segments) {
        int startLed, endLed;
        if (getSegmentRange(segmentDir.segmentId, startLed, endLed)) {
            // 创建该分段的参数副本，设置方向
            EffectParams segmentParams = preset->params;
            segmentParams.direction = segmentDir.direction;

            // 渲染灯效到该分段
            switch (preset->effectType) {
                case EffectType::BREATHING:
                    LightEffect::renderBreathing(leds, startLed, endLed, segmentParams, currentTime);
                    break;
                case EffectType::RAINBOW:
                    LightEffect::renderRainbow(leds, startLed, endLed, segmentParams, currentTime);
                    break;
                case EffectType::RUNNING:
                    LightEffect::renderRunning(leds, startLed, endLed, segmentParams, currentTime);
                    break;
                case EffectType::TEMPERATURE_MAP:
                    if (printerManager) {
                        // 实时获取温度数据
                        auto status = printerManager->getCurrentStatus();

                        // 先清空该分段的LED
                        for (int i = startLed; i <= endLed; i++) {
                            leds[i] = CRGB::Black;
                        }

                        // 渲染喷嘴温度映射（如果有颜色配置）
                        if (!segmentParams.extruderColors.empty()) {
                            LightEffect::renderTemperatureMap(leds, startLed, endLed, segmentParams.direction,
                                                             status.extruderTemp, status.extruderTarget,
                                                             segmentParams.extruderColors, segmentParams.extruderBrightness,
                                                             currentTime);
                        }

                        // 渲染热床温度映射（如果有颜色配置）
                        if (!segmentParams.bedColors.empty()) {
                            LightEffect::renderTemperatureMap(leds, startLed, endLed, segmentParams.direction,
                                                             status.bedTemp, status.bedTarget,
                                                             segmentParams.bedColors, segmentParams.bedBrightness,
                                                             currentTime);
                        }
                    }
                    break;
            }
        }
    }

    // 更新LED显示
    FastLED.show();
}

/**
 * 处理停止预览请求
 */
void EffectsManager::handleStopPreview(AsyncWebServerRequest* request) {
    if (!previewMode) {
        request->send(400, "application/json", "{\"error\":\"不在预览模式\"}");
        return;
    }

    // 清空预览数据
    previewPresetId = "";
    previewPresetData = EffectPreset();

    // 关闭所有LED
    if (ledsInitialized && leds) {
        fill_solid(leds, LED_COUNT, CRGB::Black);
        FastLED.show();
    }

    request->send(200, "application/json", "{\"message\":\"预览已停止\"}");
}

/**
 * 根据分段ID获取分段信息
 */
bool EffectsManager::getSegmentRange(const String& segmentId, int& startLed, int& endLed) const {
    if (!segmentsManager) {
        return false;
    }

    const auto* segment = segmentsManager->getSegmentById(segmentId);
    if (!segment) {
        return false;
    }

    // 转换为0基索引（分段管理器使用1基索引）
    startLed = segment->startLed - 1;
    endLed = segment->endLed - 1;

    // 确保范围有效
    if (startLed < 0) startLed = 0;
    if (endLed >= LED_COUNT) endLed = LED_COUNT - 1;
    if (startLed > endLed) return false;

    return true;
}

/**
 * 保存预设配置到文件
 */
bool EffectsManager::saveToFile() {
    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    JsonArray presetsArray = doc.createNestedArray("presets");

    for (const auto& preset : presets) {
        JsonObject presetObj = presetsArray.createNestedObject();
        preset.toJson(presetObj);
    }

    File file = LittleFS.open(configFile, "w");
    if (!file) {
        return false;
    }

    size_t bytesWritten = serializeJson(doc, file);
    file.close();

    return bytesWritten > 0;
}

/**
 * 从文件加载预设配置
 */
bool EffectsManager::loadFromFile() {
    if (!LittleFS.exists(configFile)) {
        return false;
    }

    File file = LittleFS.open(configFile, "r");
    if (!file) {
        return false;
    }

    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, file);
    file.close();

    if (error) {
        return false;
    }

    presets.clear();
    JsonArray presetsArray = doc["presets"];

    for (JsonVariant presetVar : presetsArray) {
        JsonObject presetObj = presetVar.as<JsonObject>();
        try {
            EffectPreset preset = EffectPreset::fromJson(presetObj);
            presets.push_back(preset);
        } catch (const std::exception& e) {
            // 跳过无效预设
        }
    }

    return true;
}

// =================================================================
// API处理函数
// =================================================================

/**
 * 处理获取预设列表请求
 */
void EffectsManager::handleGetPresets(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    JsonArray presetsArray = doc.createNestedArray("presets");

    for (const auto& preset : presets) {
        JsonObject presetObj = presetsArray.createNestedObject();
        preset.toJson(presetObj);

        // 添加分段名称信息
        JsonArray segmentNamesArray = presetObj.createNestedArray("segmentNames");
        for (const auto& segment : preset.segments) {
            if (segmentsManager) {
                const auto* segmentInfo = segmentsManager->getSegmentById(segment.segmentId);
                if (segmentInfo) {
                    segmentNamesArray.add(segmentInfo->name);
                }
            }
        }
    }

    sendJsonResponse(request, HTTP_OK, "获取预设列表成功", &doc);
}

/**
 * 处理创建预设请求
 */
void EffectsManager::handleCreatePreset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len != total) {
        return; // 等待所有数据
    }

    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, data, len);

    if (error) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败: " + String(error.c_str()));
        return;
    }

    try {
        EffectPreset preset = EffectPreset::fromJson(doc.as<JsonObject>());

        // 生成新的ID
        preset.id = generatePresetId();
        preset.createdTime = millis();

        if (addPreset(preset)) {
            StaticJsonDocument<512> responseDoc;
            responseDoc["presetId"] = preset.id;
            sendJsonResponse(request, HTTP_OK, "预设创建成功", &responseDoc);
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "预设创建失败");
        }
    } catch (const std::exception& e) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "预设数据无效: " + String(e.what()));
    }
}

/**
 * 处理更新预设请求
 */
void EffectsManager::handleUpdatePreset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len != total) {
        return; // 等待所有数据
    }

    // 从URL中提取预设ID
    String path = request->url();
    int lastSlash = path.lastIndexOf('/');
    if (lastSlash == -1) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "无效的URL格式");
        return;
    }
    String presetId = path.substring(lastSlash + 1);

    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    DeserializationError error = deserializeJson(doc, data, len);

    if (error) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON解析失败: " + String(error.c_str()));
        return;
    }

    try {
        EffectPreset preset = EffectPreset::fromJson(doc.as<JsonObject>());

        if (updatePreset(presetId, preset)) {
            sendJsonResponse(request, HTTP_OK, "预设更新成功");
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "预设更新失败");
        }
    } catch (const std::exception& e) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "预设数据无效: " + String(e.what()));
    }
}

/**
 * 处理删除预设请求
 */
void EffectsManager::handleDeletePreset(AsyncWebServerRequest* request) {
    // 从URL中提取预设ID
    String path = request->url();
    int lastSlash = path.lastIndexOf('/');
    if (lastSlash == -1) {
        sendErrorResponse(request, HTTP_BAD_REQUEST, "无效的URL格式");
        return;
    }
    String presetId = path.substring(lastSlash + 1);

    if (deletePreset(presetId)) {
        sendJsonResponse(request, HTTP_OK, "预设删除成功");
    } else {
        sendErrorResponse(request, HTTP_NOT_FOUND, "预设不存在或删除失败");
    }
}

/**
 * 处理停止灯效请求
 */
void EffectsManager::handleStopEffect(AsyncWebServerRequest* request) {
    stopCurrentEffect();
    sendJsonResponse(request, HTTP_OK, "灯效已停止");
}

/**
 * 处理获取统计信息请求
 */
void EffectsManager::handleGetStats(AsyncWebServerRequest* request) {
    StaticJsonDocument<512> doc;
    doc["totalPresets"] = presets.size();
    doc["currentPreset"] = getCurrentPresetId();
    doc["isRunning"] = isEffectRunning();

    sendJsonResponse(request, HTTP_OK, "获取统计信息成功", &doc);
}

/**
 * 处理获取可用灯效类型请求
 */
void EffectsManager::handleGetAvailableEffects(AsyncWebServerRequest* request) {
    auto effectList = LightEffect::getAllEffectInfo();

    StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
    JsonArray effectsArray = doc.createNestedArray("effects");

    for (const auto& effect : effectList) {
        JsonObject effectObj = effectsArray.createNestedObject();
        effectObj["type"] = static_cast<int>(effect.type);
        effectObj["name"] = effect.name;
        effectObj["description"] = effect.description;

        JsonArray paramsArray = effectObj.createNestedArray("availableParams");
        for (const auto& param : effect.availableParams) {
            JsonObject paramObj = paramsArray.createNestedObject();
            paramObj["type"] = static_cast<int>(param.type);
            paramObj["displayName"] = param.displayName;
            paramObj["minValue"] = param.minValue;
            paramObj["maxValue"] = param.maxValue;
            paramObj["defaultValue"] = param.defaultValue;
            paramObj["unit"] = param.unit;
        }
    }

    sendJsonResponse(request, HTTP_OK, "获取可用灯效成功", &doc);
}

// =================================================================
// 工具函数
// =================================================================

/**
 * 发送JSON响应
 */
void EffectsManager::sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
    responseDoc["success"] = (code == HTTP_OK);
    responseDoc["message"] = message;
    responseDoc["timestamp"] = millis();

    if (data) {
        responseDoc["data"] = *data;
    }

    String response;
    serializeJson(responseDoc, response);

    request->send(code, "application/json", response);
}

/**
 * 发送错误响应
 */
void EffectsManager::sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message) {
    sendJsonResponse(request, code, message);
}

// =================================================================
// 预览功能实现
// =================================================================

/**
 * 启用预览模式
 */
bool EffectsManager::enablePreviewMode() {
    // 设置预览模式标志
    previewMode = true;

    // 暂停场景自动化
    if (scenesManager) {
        scenesManager->disableScenes();
    }

    // 停止当前正在运行的灯效
    stopCurrentEffect();

    // 关闭所有LED
    if (ledsInitialized && leds) {
        fill_solid(leds, LED_COUNT, CRGB::Black);
        FastLED.show();
    }

    return true;
}

/**
 * 禁用预览模式
 */
bool EffectsManager::disablePreviewMode() {
    if (!previewMode) {
        return true; // 已经不在预览模式
    }

    // 停止预览
    stopPreview();

    // 清除预览数据
    previewPresetId = "";
    previewPresetData = EffectPreset();

    // 设置预览模式标志为false
    previewMode = false;

    // 恢复场景自动化
    if (scenesManager) {
        scenesManager->enableScenes();

        // 强制重新激活当前场景的灯效
        // 因为预览模式关闭了LED，需要重新点亮
        scenesManager->forceReactivateCurrentScene();
    }

    // 注意：不要在这里清空LED，让场景系统重新激活灯效

    return true;
}

/**
 * 检查是否在预览模式
 */
bool EffectsManager::isPreviewMode() const {
    return previewMode;
}

/**
 * 预览指定的灯效预设
 */
bool EffectsManager::previewPreset(const EffectPreset& preset) {
    if (!previewMode) {
        return false;
    }

    // 保存预览数据到临时变量
    previewPresetData = preset;
    previewPresetId = "preview_temp";

    // 立即渲染预览效果
    renderPreviewEffect();

    return true;
}

/**
 * 停止当前预览
 */
bool EffectsManager::stopPreview() {
    if (!previewMode) {
        return true;
    }

    // 清除预览数据
    previewPresetId = "";
    previewPresetData = EffectPreset();

    // 关闭所有LED
    if (ledsInitialized && leds) {
        fill_solid(leds, LED_COUNT, CRGB::Black);
        FastLED.show();
    }

    return true;
}

/**
 * 渲染预览灯效
 */
void EffectsManager::renderPreviewEffect() {
    if (!ledsInitialized || !leds || !previewMode || previewPresetId.isEmpty()) {
        return;
    }

    // 检查渲染频率限制（使用与正常模式相同的帧率）
    unsigned long currentTime = millis();
    if (currentTime - lastPreviewTime < (1000 / EFFECT_FPS_DEFAULT)) {
        return;
    }
    lastPreviewTime = currentTime;

    // 先清空所有LED，确保未选中的分段被熄灭
    fill_solid(leds, LED_COUNT, CRGB::Black);

    // 使用预览数据渲染灯效
    // 这里复用现有的灯效渲染逻辑，但使用临时预览数据
    if (!previewPresetData.segments.empty() && segmentsManager) {
        for (const auto& segmentDir : previewPresetData.segments) {
            const LEDSegment* segment = segmentsManager->getSegmentById(segmentDir.segmentId);
            if (segment) {
                // 渲染到指定分段
                int startLed = segment->startLed - 1; // 转换为0基索引
                int endLed = segment->endLed - 1;

                if (startLed >= 0 && endLed < LED_COUNT && startLed <= endLed) {
                    // 创建该分段的参数副本，设置方向
                    EffectParams segmentParams = previewPresetData.params;
                    segmentParams.direction = segmentDir.direction;

                    // 渲染灯效到该分段
                    switch (previewPresetData.effectType) {
                        case EffectType::BREATHING:
                            LightEffect::renderBreathing(leds, startLed, endLed, segmentParams, currentTime);
                            break;
                        case EffectType::RAINBOW:
                            LightEffect::renderRainbow(leds, startLed, endLed, segmentParams, currentTime);
                            break;
                        case EffectType::RUNNING:
                            LightEffect::renderRunning(leds, startLed, endLed, segmentParams, currentTime);
                            break;
                        case EffectType::TEMPERATURE_MAP:
                            // 温度映射灯效不支持预览，需要实际温度数据
                            break;
                    }
                }
            }
        }

        FastLED.show();
    }
}

// =================================================================
// 预览模式API处理函数
// =================================================================

/**
 * 处理启用预览模式请求
 */
void EffectsManager::handleEnablePreview(AsyncWebServerRequest* request) {
    try {
        if (enablePreviewMode()) {
            StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> doc;
            doc["success"] = true;
            doc["message"] = "预览模式已启用";
            doc["previewMode"] = true;

            String response;
            serializeJson(doc, response);
            request->send(HTTP_OK, "application/json", response);
        } else {
            sendErrorResponse(request, HTTP_INTERNAL_ERROR, "启用预览模式失败");
        }
    } catch (const std::exception& e) {
        sendErrorResponse(request, HTTP_INTERNAL_ERROR, "服务器内部错误");
    } catch (...) {
        sendErrorResponse(request, HTTP_INTERNAL_ERROR, "服务器内部错误");
    }
}

/**
 * 处理禁用预览模式请求
 */
void EffectsManager::handleDisablePreview(AsyncWebServerRequest* request) {
    try {
        if (disablePreviewMode()) {
            StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> doc;
            doc["success"] = true;
            doc["message"] = "预览模式已禁用";
            doc["previewMode"] = false;

            String response;
            serializeJson(doc, response);
            request->send(HTTP_OK, "application/json", response);
        } else {
            sendErrorResponse(request, HTTP_INTERNAL_ERROR, "禁用预览模式失败");
        }
    } catch (const std::exception& e) {
        sendErrorResponse(request, HTTP_INTERNAL_ERROR, "服务器内部错误");
    } catch (...) {
        sendErrorResponse(request, HTTP_INTERNAL_ERROR, "服务器内部错误");
    }
}

/**
 * 处理获取预览状态请求
 */
void EffectsManager::handleGetPreviewStatus(AsyncWebServerRequest* request) {
    StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> doc;
    doc["success"] = true;
    doc["message"] = "获取预览状态成功";

    JsonObject data = doc.createNestedObject("data");
    data["previewMode"] = previewMode;
    data["currentPreviewId"] = previewPresetId;

    String response;
    serializeJson(doc, response);
    request->send(HTTP_OK, "application/json", response);
}

/**
 * 处理预览灯效请求
 */
void EffectsManager::handlePreviewEffect(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total) {
    if (index + len == total) {
        // 完整数据接收完毕，开始处理
        StaticJsonDocument<JSON_EFFECTS_BUFFER_SIZE> doc;
        DeserializationError error = deserializeJson(doc, (char*)data, len);

        if (error) {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "JSON格式错误");
            return;
        }

        // 创建临时预设用于预览
        EffectPreset tempPreset;
        tempPreset.id = "preview_temp";
        tempPreset.name = doc["name"].as<String>();
        tempPreset.effectType = static_cast<EffectType>(doc["effectType"].as<int>());

        // 解析分段配置
        JsonArray segmentsArray = doc["segments"];
        for (JsonVariant segmentVar : segmentsArray) {
            JsonObject segmentObj = segmentVar.as<JsonObject>();
            SegmentDirection segDir;
            segDir.segmentId = segmentObj["segmentId"].as<String>();
            segDir.direction = segmentObj["direction"].as<uint8_t>();
            tempPreset.segments.push_back(segDir);
        }

        // 解析参数配置
        JsonObject paramsObj = doc["params"];
        tempPreset.params.brightness = paramsObj["brightness"].as<uint8_t>();
        tempPreset.params.speed = paramsObj["speed"].as<uint8_t>();
        tempPreset.params.direction = paramsObj["direction"].as<uint8_t>();
        tempPreset.params.intensity = paramsObj["intensity"].as<uint8_t>();

        // 解析颜色数组
        tempPreset.params.colors.clear();
        JsonArray colorsArray = paramsObj["colors"];
        for (JsonVariant colorVar : colorsArray) {
            JsonObject colorObj = colorVar.as<JsonObject>();
            tempPreset.params.colors.push_back(CRGB(
                colorObj["r"].as<uint8_t>(),
                colorObj["g"].as<uint8_t>(),
                colorObj["b"].as<uint8_t>()
            ));
        }

        // 如果没有颜色数据，设置默认红色
        if (tempPreset.params.colors.empty()) {
            tempPreset.params.colors.push_back(CRGB::Red);
        }

        // 执行预览
        if (this->previewPreset(tempPreset)) {
            StaticJsonDocument<JSON_RESPONSE_BUFFER_SIZE> responseDoc;
            responseDoc["success"] = true;
            responseDoc["message"] = "预览已更新";

            String response;
            serializeJson(responseDoc, response);
            request->send(HTTP_OK, "application/json", response);
        } else {
            sendErrorResponse(request, HTTP_BAD_REQUEST, "预览失败");
        }
    }
}
