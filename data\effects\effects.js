// 灯效预设模块

class EffectsManager {
    constructor() {
        this.effects = [];
        this.availableEffects = [];
        this.availableSegments = [];
        this.currentEditingId = null;

        // 预览模式相关
        this.previewMode = false;
        this.previewToggle = null;
        this.previewTimeout = null;
        this.previewIndicator = null;

        this.init();
    }

    // 初始化
    init() {
        console.log('初始化灯效预设模块...');
        this.bindEvents();
        this.bindPreviewEvents();
        this.createPreviewIndicator();
        this.loadData();
    }

    // 加载数据
    async loadData() {
        await this.loadAvailableEffects();
        await this.loadAvailableSegments();
        await this.loadEffects();
        this.renderEffects();
    }

    // 绑定事件
    bindEvents() {
        // 添加灯效按钮
        const addBtn = document.querySelector('#effects-module .add-effect-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showModal());
        }

        // 模态框关闭事件 - 只通过关闭按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('module-modal-effects-close')) {
                this.hideModal();
            }
        });

        // 取消按钮事件
        const cancelBtn = document.getElementById('effect-cancel-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.hideModal());
        }

        // 表单提交事件
        const form = document.getElementById('effect-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }

        // 灯效类型变化事件
        const effectTypeSelect = document.getElementById('effect-type');
        if (effectTypeSelect) {
            effectTypeSelect.addEventListener('change', () => {
                this.updateEffectTypeDescription();
                this.updateParamsContainer();
                this.updateDirectionDisplay();

                // 触发实时预览
                this.triggerPreview();
            });
        }

        // 监听分段变化事件
        document.addEventListener('segmentChanged', () => {
            this.refreshSegments();
        });
    }

    // 加载可用灯效类型
    async loadAvailableEffects() {
        try {
            // 添加时间戳避免缓存
            const response = await fetch('/api/effects/available?t=' + Date.now());
            const data = await response.json();

            if (data.success) {
                this.availableEffects = data.data.effects || [];
                console.log('加载可用灯效类型成功:', this.availableEffects);


            } else {
                console.error('加载可用灯效类型失败:', data.message);
                this.showError('加载可用灯效类型失败: ' + data.message);
            }
        } catch (error) {
            console.error('加载可用灯效类型出错:', error);
            this.showError('加载可用灯效类型出错: ' + error.message);
        }
    }

    // 加载可用分段
    async loadAvailableSegments() {
        try {
            const response = await fetch('/api/segments');
            const data = await response.json();

            if (data.success) {
                this.availableSegments = data.data.segments || [];
                console.log('加载可用分段成功:', this.availableSegments);
            } else {
                console.error('加载可用分段失败:', data.message);
                this.showError('加载可用分段失败: ' + data.message);
            }
        } catch (error) {
            console.error('加载可用分段出错:', error);
            this.showError('加载可用分段出错: ' + error.message);
        }
    }

    // 加载灯效列表
    async loadEffects() {
        try {
            const response = await fetch('/api/effects');
            const data = await response.json();

            if (data.success) {
                this.effects = data.data.presets || [];
                console.log('加载灯效列表成功:', this.effects);
            } else {
                console.error('加载灯效列表失败:', data.message);
                this.showError('加载灯效列表失败: ' + data.message);
            }
        } catch (error) {
            console.error('加载灯效列表出错:', error);
            this.showError('加载灯效列表出错: ' + error.message);
        }
    }

    // 渲染灯效列表
    renderEffects() {
        const container = document.querySelector('#effects-module .card-content');
        if (!container) return;

        if (this.effects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">💡</div>
                    <div class="empty-state-text">暂无灯效</div>
                    <div class="empty-state-hint">点击右上角的 + 号添加新灯效</div>
                </div>
            `;
        } else {
            const effectsHtml = this.effects.map(effect => this.createEffectItemHtml(effect)).join('');
            container.innerHTML = `<div class="effects-list">${effectsHtml}</div>`;
        }
    }

    // 创建灯效项HTML
    createEffectItemHtml(effect) {
        const effectTypeInfo = this.availableEffects.find(e => e.type === effect.effectType);
        const effectTypeName = effectTypeInfo ? effectTypeInfo.name : '未知类型';

        // 获取分段名称
        const segmentNames = effect.segments.map(seg => {
            const segment = this.availableSegments.find(s => s.id === seg.segmentId);
            return segment ? segment.name : seg.segmentId;
        }).join(', ');

        // 获取参数信息
        let paramsText = `亮度:${effect.params.brightness}`;
        if (effect.params.speed !== undefined) {
            paramsText += ` 速度:${effect.params.speed}`;
        }
        if (effect.params.intensity !== undefined) {
            paramsText += ` 强度:${effect.params.intensity}`;
        }

        // 显示颜色数量
        if (effect.params.colors && Array.isArray(effect.params.colors)) {
            paramsText += ` 颜色:${effect.params.colors.length}个`;
        } else if (effect.params.color) {
            paramsText += ` 颜色:1个`;
        }

        // 获取图标
        const icon = this.getEffectIcon(effect.effectType);

        return `
            <div class="effect-item">
                <div class="effect-header">
                    <div class="effect-info">
                        <span class="effect-icon">${icon}</span>
                        <span class="effect-name">${effect.name}</span>
                    </div>
                    <div class="effect-actions">
                        <button class="effect-edit-btn" onclick="effectsManager.editEffect('${effect.id}')">编辑</button>
                        <button class="effect-delete-btn" onclick="effectsManager.deleteEffect('${effect.id}')">删除</button>
                    </div>
                </div>
                <div class="effect-details">
                    <div class="effect-type">${effectTypeName} | ${segmentNames}</div>
                    <div class="effect-params">${paramsText}</div>
                </div>
            </div>
        `;
    }

    // 获取灯效图标
    getEffectIcon(effectType) {
        const icons = {
            0: '💨', // 呼吸灯
            1: '🌈', // 彩虹
            2: '🏃', // 跑马灯
            3: '🌡️'  // 温度映射
        };
        return icons[effectType] || '✨';
    }

    // 编辑灯效
    editEffect(id) {
        const effect = this.effects.find(e => e.id === id);
        if (effect) {
            this.currentEditingId = id;
            this.showModal(effect);
        }
    }

    // 删除灯效
    async deleteEffect(id) {
        const effect = this.effects.find(e => e.id === id);
        if (!effect) {
            console.error('未找到灯效:', id);
            return;
        }

        if (!confirm(`确定要删除灯效 "${effect.name}" 吗？`)) {
            return;
        }

        try {
            const response = await fetch(`/api/effects/${id}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('灯效删除成功');
                await this.loadEffects();
                this.renderEffects();

                // 发送灯效变化事件通知其他模块
                document.dispatchEvent(new CustomEvent('effectChanged', {
                    detail: {
                        action: 'deleted',
                        timestamp: Date.now()
                    }
                }));
            } else {
                this.showError('删除失败: ' + data.message);
            }
        } catch (error) {
            console.error('删除灯效出错:', error);
            this.showError('删除灯效出错: ' + error.message);
        }
    }

    // 显示模态框
    showModal(editData = null) {
        const modal = document.getElementById('effect-modal');
        const titleElement = document.getElementById('effect-modal-title');

        if (modal) {
            // 设置标题
            if (titleElement) {
                titleElement.textContent = editData ? '编辑灯效预设' : '添加灯效预设';
            }

            // 初始化表单
            this.initializeForm();

            // 如果是编辑模式，填充数据
            if (editData) {
                this.fillFormData(editData);
            }

            modal.classList.add('show');

            // 如果在预览模式下，延迟触发预览（等待DOM更新）
            if (this.previewMode) {
                setTimeout(() => {
                    this.triggerPreview();
                }, 100);
            }
        }
    }

    // 初始化表单
    initializeForm() {
        // 更新灯效类型选项
        this.updateEffectTypeOptions();

        // 如果不是编辑模式，清空表单
        if (!this.currentEditingId) {
            document.getElementById('effect-name').value = '';
            document.getElementById('effect-type').value = '';

            // 清空参数容器
            document.getElementById('params-container').innerHTML = '';

            // 初始化时隐藏所有方向选择
            this.updateDirectionDisplay();

            // 清空灯效类型描述
            this.updateEffectTypeDescription();
        }

        // 清除错误信息
        this.clearFormErrors();
    }

    // 更新灯效类型选项
    updateEffectTypeOptions() {
        const select = document.getElementById('effect-type');
        if (!select) return;

        select.innerHTML = '<option value="">请选择灯效类型</option>';

        this.availableEffects.forEach(effect => {
            const option = document.createElement('option');
            option.value = effect.type;
            option.textContent = effect.name;
            select.appendChild(option);
        });
    }

    // 更新灯效类型描述
    updateEffectTypeDescription() {
        const select = document.getElementById('effect-type');
        const descriptionElement = document.getElementById('effect-type-description');

        if (!select || !descriptionElement) return;

        const selectedType = parseInt(select.value);

        if (isNaN(selectedType)) {
            // 没有选择灯效类型，隐藏描述
            descriptionElement.classList.remove('show');
            descriptionElement.textContent = '';
        } else {
            // 找到对应的灯效信息
            const effectInfo = this.availableEffects.find(effect => effect.type === selectedType);

            if (effectInfo && effectInfo.description) {
                descriptionElement.textContent = effectInfo.description;
                descriptionElement.classList.add('show');
            } else {
                descriptionElement.classList.remove('show');
                descriptionElement.textContent = '';
            }
        }
    }



    // 隐藏模态框
    hideModal() {
        const modal = document.getElementById('effect-modal');
        if (modal) {
            modal.classList.remove('show');
        }

        // 如果在预览模式下，关闭模态框时停止预览
        if (this.previewMode) {
            this.sendStopPreviewRequest();
        }
    }



    // 绑定分段选择事件
    bindSegmentEvents() {
        const segmentButtons = document.querySelectorAll('.segment-button');

        segmentButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();

                // 切换选中状态
                button.classList.toggle('selected');

                // 更新方向按钮显示
                this.updateSegmentDirectionButtons(button);

                // 触发实时预览
                this.triggerPreview();
            });

            // 绑定方向按钮事件
            const directionBtns = button.querySelectorAll('.direction-btn');
            directionBtns.forEach(dirBtn => {
                dirBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止冒泡到分段按钮

                    // 切换方向按钮状态
                    directionBtns.forEach(btn => btn.classList.remove('active'));
                    dirBtn.classList.add('active');

                    // 触发实时预览
                    this.triggerPreview();
                });
            });
        });
    }

    // 更新分段方向按钮显示
    updateSegmentDirectionButtons(segmentButton) {
        const effectType = document.getElementById('effect-type').value;
        const effectInfo = this.availableEffects.find(e => e.type == effectType);
        const supportsDirection = effectInfo && effectInfo.availableParams.some(p => p.type === 3);

        const directionButtons = segmentButton.querySelector('.segment-direction-buttons');
        if (supportsDirection && segmentButton.classList.contains('selected')) {
            directionButtons.style.display = 'flex';
        } else {
            directionButtons.style.display = 'none';
        }
    }

    // 填充表单数据
    fillFormData(data) {
        // 填充基本信息
        document.getElementById('effect-name').value = data.name || '';
        document.getElementById('effect-type').value = data.effectType !== undefined ? data.effectType : '';

        // 触发类型变化事件，生成参数控件
        document.getElementById('effect-type').dispatchEvent(new Event('change'));

        // 直接调用更新参数容器，确保参数控件已生成
        this.updateParamsContainer();

        // 延迟填充分段选择和参数（确保DOM元素已生成）
        setTimeout(() => {
            // 填充分段选择
            if (data.segments && data.segments.length > 0) {
                data.segments.forEach(segment => {
                    const segmentButton = document.querySelector(`[data-segment-id="${segment.segmentId}"]`);
                    if (segmentButton) {
                        segmentButton.classList.add('selected');

                        // 设置方向值
                        const directionBtns = segmentButton.querySelectorAll('.direction-btn');
                        directionBtns.forEach(btn => btn.classList.remove('active'));
                        const targetBtn = segmentButton.querySelector(`[data-direction="${segment.direction || 0}"]`);
                        if (targetBtn) {
                            targetBtn.classList.add('active');
                        }
                    }
                });
            }

            // 更新方向显示（基于当前选择的灯效类型）
            this.updateDirectionDisplay();
        }, 50);

        // 填充参数（延迟执行，确保参数控件已生成）
        setTimeout(() => {
            if (data.params) {
                // 亮度
                if (data.params.brightness !== undefined) {
                    const slider = document.getElementById('param-0');
                    if (slider) {
                        slider.value = data.params.brightness;
                        slider.dispatchEvent(new Event('input'));
                    }
                }

                // 速度
                if (data.params.speed !== undefined) {
                    const slider = document.getElementById('param-1');
                    if (slider) {
                        slider.value = data.params.speed;
                        slider.dispatchEvent(new Event('input'));
                    }
                }

                // 颜色 - 支持多颜色
                if (data.params.colors !== undefined && Array.isArray(data.params.colors)) {
                    this.fillMultiColorData(data.params.colors);
                } else if (data.params.color !== undefined) {
                    // 兼容旧的单色格式
                    this.fillMultiColorData([data.params.color]);
                }

                // 强度
                if (data.params.intensity !== undefined) {
                    const slider = document.getElementById('param-4');
                    if (slider) {
                        slider.value = data.params.intensity;
                        slider.dispatchEvent(new Event('input'));
                    }
                }
            }
        }, 100);
    }

    // 填充多颜色数据
    fillMultiColorData(colors) {
        const colorList = document.getElementById('color-list');
        if (!colorList) return;

        // 清空现有颜色项（保留添加按钮）
        const addColorItem = colorList.querySelector('.add-color-item');
        colorList.innerHTML = '';

        // 添加颜色项
        colors.forEach((color, index) => {
            const hex = '#' + ((color.r << 16) | (color.g << 8) | color.b).toString(16).padStart(6, '0');
            const colorItem = document.createElement('div');
            colorItem.className = 'color-item';
            colorItem.draggable = true;
            colorItem.dataset.colorIndex = index;
            colorItem.innerHTML = `
                <input type="color" class="color-picker"
                       value="${hex}"
                       data-color-index="${index}">
                <button type="button" class="delete-btn" title="删除此颜色" style="display: ${colors.length > 1 ? 'block' : 'none'};">×</button>
            `;
            colorList.appendChild(colorItem);
        });

        // 重新添加添加按钮
        if (addColorItem) {
            colorList.appendChild(addColorItem);
        } else {
            const newAddItem = document.createElement('div');
            newAddItem.className = 'add-color-item';
            newAddItem.id = 'add-color-item';
            newAddItem.title = '添加新颜色';
            newAddItem.textContent = '+';
            colorList.appendChild(newAddItem);
        }

        // 更新显示
        this.updateColorDisplay();
        this.bindColorItemEvents();
        this.bindDragEvents();
    }

    // 显示成功消息
    showSuccess(message) {
        if (window.AppUtils && window.AppUtils.showSuccess) {
            window.AppUtils.showSuccess(message);
        } else {
            alert(message);
        }
    }

    // 显示错误消息
    showError(message) {
        if (window.AppUtils && window.AppUtils.showError) {
            window.AppUtils.showError(message);
        } else {
            alert(message);
        }
    }

    // 更新参数容器
    updateParamsContainer() {
        const effectType = document.getElementById('effect-type').value;
        const container = document.getElementById('params-container');

        if (!container || !effectType) {
            if (container) container.innerHTML = '';
            return;
        }

        const effectInfo = this.availableEffects.find(e => e.type == effectType);
        if (!effectInfo) {
            container.innerHTML = '';
            return;
        }

        // 温度映射灯效使用特殊的双区域布局
        if (effectType == 3) { // TEMPERATURE_MAP
            // 保存原始状态
            const originalId = container.id;
            const originalHTML = container.innerHTML;

            // 第一次生成（喷嘴参数）
            container.id = 'extruder-params-container';
            container.innerHTML = '';
            this.generateSingleParams(effectInfo, container);
            const extruderParams = container.innerHTML;

            // 第二次生成（热床参数）
            container.id = 'bed-params-container';
            container.innerHTML = '';
            this.generateSingleParams(effectInfo, container);
            const bedParams = container.innerHTML;

            // 恢复原始状态并组合结果
            container.id = originalId;
            container.innerHTML = `
                <div class="temperature-mapping-container">
                    <div class="temp-config-area" data-temp-source="extruder">
                        <h4>🌡️ 喷嘴温度映射</h4>
                        ${extruderParams}
                    </div>
                    <div class="temp-config-area" data-temp-source="bed">
                        <h4>🛏️ 热床温度映射</h4>
                        ${bedParams}
                    </div>
                </div>
            `;

            // 绑定事件
            this.bindParamEvents(container);
            return;
        }

        // 其他灯效使用原有的单区域布局
        this.generateSingleParams(effectInfo, container);

        // 清理旧的事件监听器，然后绑定新的参数事件
        this.bindParamEvents(container);
    }

    // 更新方向显示（新的按钮逻辑）
    updateDirectionDisplay() {
        // 获取所有分段按钮
        const segmentButtons = document.querySelectorAll('.segment-button');

        segmentButtons.forEach(button => {
            this.updateSegmentDirectionButtons(button);
        });
    }

    // 绑定参数控件事件
    bindParamEvents(container) {
        // 绑定滑块事件
        container.querySelectorAll('.param-slider').forEach(slider => {
            slider.addEventListener('input', (e) => {
                const valueSpan = document.getElementById(`param-${e.target.dataset.paramType}-value`);
                if (valueSpan) {
                    valueSpan.textContent = e.target.value;
                }

                // 触发实时预览
                this.triggerPreview();
            });
        });

        // 绑定多颜色选择器事件
        this.bindMultiColorEvents();

        // 绑定分段选择事件
        this.bindSegmentEvents();
    }

    // 绑定多颜色选择器事件
    bindMultiColorEvents() {
        const addColorItem = document.getElementById('add-color-item');
        if (addColorItem) {
            addColorItem.addEventListener('click', () => this.addColor());
        }

        // 绑定现有颜色项的事件
        this.bindColorItemEvents();

        // 绑定拖拽事件
        this.bindDragEvents();
    }

    // 绑定颜色项事件
    bindColorItemEvents() {
        const colorItems = document.querySelectorAll('.color-item');
        colorItems.forEach(item => {
            // 绑定颜色选择器事件
            const colorPicker = item.querySelector('.color-picker');
            if (colorPicker) {
                colorPicker.addEventListener('input', () => {
                    // 触发实时预览
                    this.triggerPreview();
                });
            }

            // 绑定删除按钮事件
            const deleteBtn = item.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const colorIndex = parseInt(item.dataset.colorIndex);
                    this.removeColor(colorIndex);
                });
            }
        });
    }

    // 绑定拖拽事件
    bindDragEvents() {
        const colorList = document.getElementById('color-list');
        if (!colorList) return;

        let draggedElement = null;
        let placeholder = null;

        // 为所有颜色项绑定拖拽事件
        const colorItems = colorList.querySelectorAll('.color-item');
        colorItems.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                draggedElement = item;
                item.classList.add('dragging');

                // 创建占位符
                placeholder = document.createElement('div');
                placeholder.className = 'drag-placeholder';

                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', item.outerHTML);
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
                if (placeholder && placeholder.parentNode) {
                    placeholder.parentNode.removeChild(placeholder);
                }
                draggedElement = null;
                placeholder = null;

                // 重新编号
                this.reindexColorItems();
            });
        });

        // 为容器绑定拖拽事件
        colorList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            const afterElement = this.getDragAfterElement(colorList, e.clientX);
            if (placeholder) {
                if (afterElement == null) {
                    // 在添加按钮前插入
                    const addColorItem = colorList.querySelector('.add-color-item');
                    if (addColorItem) {
                        colorList.insertBefore(placeholder, addColorItem);
                    }
                } else {
                    colorList.insertBefore(placeholder, afterElement);
                }
            }
        });

        colorList.addEventListener('drop', (e) => {
            e.preventDefault();
            if (draggedElement && placeholder) {
                placeholder.parentNode.replaceChild(draggedElement, placeholder);
            }
        });
    }

    // 获取拖拽后的插入位置
    getDragAfterElement(container, x) {
        const draggableElements = [...container.querySelectorAll('.color-item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = x - box.left - box.width / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    // 添加颜色（在末尾）
    addColor() {
        const colorList = document.getElementById('color-list');
        const colorItems = colorList.querySelectorAll('.color-item');

        if (colorItems.length >= 10) {
            this.showError('最多只能添加10个颜色');
            return;
        }

        const newColorItem = document.createElement('div');
        newColorItem.className = 'color-item';
        newColorItem.draggable = true;
        newColorItem.dataset.colorIndex = colorItems.length;
        newColorItem.innerHTML = `
            <input type="color" class="color-picker"
                   value="#ff0000"
                   data-color-index="${colorItems.length}">
            <button type="button" class="delete-btn" title="删除此颜色">×</button>
        `;

        // 在添加按钮前插入
        const addColorItem = colorList.querySelector('.add-color-item');
        colorList.insertBefore(newColorItem, addColorItem);

        this.updateColorDisplay();
        this.bindColorItemEvents();
        this.bindDragEvents();

        // 触发实时预览
        this.triggerPreview();
    }

    // 删除颜色
    removeColor(colorIndex) {
        const colorList = document.getElementById('color-list');
        const colorItems = colorList.querySelectorAll('.color-item');

        if (colorItems.length <= 1) {
            this.showError('至少需要保留一个颜色');
            return;
        }

        // 删除指定的颜色项
        const targetItem = colorList.querySelector(`[data-color-index="${colorIndex}"]`);
        if (targetItem) {
            targetItem.remove();
        }

        // 重新编号所有颜色项
        this.reindexColorItems();
        this.updateColorDisplay();

        // 触发实时预览
        this.triggerPreview();
    }

    // 重新编号颜色项
    reindexColorItems() {
        const colorList = document.getElementById('color-list');
        const colorItems = colorList.querySelectorAll('.color-item');

        colorItems.forEach((item, index) => {
            item.dataset.colorIndex = index;
            const colorPicker = item.querySelector('.color-picker');
            if (colorPicker) {
                colorPicker.dataset.colorIndex = index;
            }
        });
    }

    // 更新颜色显示
    updateColorDisplay() {
        const colorList = document.getElementById('color-list');
        const colorItems = colorList.querySelectorAll('.color-item');
        const colorCount = colorItems.length;

        // 更新颜色计数显示
        const colorCountSpan = document.getElementById('color-count');
        if (colorCountSpan) {
            colorCountSpan.textContent = colorCount;
        }

        // 更新删除按钮显示状态
        colorItems.forEach(item => {
            const deleteBtn = item.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.style.display = colorCount > 1 ? 'block' : 'none';
            }
        });

        // 更新添加按钮状态
        const addColorItem = document.getElementById('add-color-item');
        if (addColorItem) {
            if (colorCount >= 10) {
                addColorItem.style.opacity = '0.5';
                addColorItem.style.cursor = 'not-allowed';
                addColorItem.title = '已达上限';
            } else {
                addColorItem.style.opacity = '1';
                addColorItem.style.cursor = 'pointer';
                addColorItem.title = '添加新颜色';
            }
        }
    }

    // 创建滑块参数
    createSliderParam(param) {
        return `
            <div class="effects-form-group compact">
                <div class="param-row">
                    <label class="effects-form-label">${param.displayName} <span>*</span></label>
                    <input type="range" class="param-slider"
                           id="param-${param.type}"
                           min="${param.minValue}"
                           max="${param.maxValue}"
                           value="${param.defaultValue}"
                           data-param-type="${param.type}">
                    <span class="param-value" id="param-${param.type}-value">${param.defaultValue}</span>
                </div>
            </div>
        `;
    }

    // 创建颜色参数
    createColorParam(param) {
        const defaultColor = '#' + param.defaultValue.toString(16).padStart(6, '0');
        return `
            <div class="effects-form-group compact">
                <div class="param-row">
                    <label class="effects-form-label">${param.displayName} <span>*</span></label>
                    <div class="multi-color-container" id="multi-color-container">
                        <div class="color-list" id="color-list">
                            <div class="color-item" draggable="true" data-color-index="0">
                                <input type="color" class="color-picker"
                                       value="${defaultColor}"
                                       data-color-index="0">
                                <button type="button" class="delete-btn" title="删除此颜色" style="display: none;">×</button>
                            </div>
                            <div class="add-color-item" id="add-color-item" title="添加新颜色">+</div>
                        </div>
                        <div class="color-count-hint">
                            <span id="color-count">1</span>/10 个颜色 | 拖拽可重新排序
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 创建分段选择参数
    createSegmentsSelection() {
        if (this.availableSegments.length === 0) {
            return `
                <div class="effects-form-group compact">
                    <div class="param-row">
                        <label class="effects-form-label">应用分段 <span>*</span></label>
                        <div class="effects-form-hint">暂无可用分段，请先创建分段</div>
                    </div>
                    <div class="effects-form-error" id="segments-error"></div>
                </div>
            `;
        }

        return `
            <div class="effects-form-group compact">
                <div class="param-row">
                    <label class="effects-form-label">应用分段 <span>*</span></label>
                    <div class="segments-selection" id="segments-selection">
                        <div class="segments-buttons">
                            ${this.availableSegments.map(segment => `
                                <div class="segment-button" data-segment-id="${segment.id}">
                                    <span class="segment-name">${segment.name}</span>
                                    <div class="segment-direction-buttons" style="display: none;">
                                        <button type="button" class="direction-btn active" data-direction="0">正</button>
                                        <button type="button" class="direction-btn" data-direction="1">反</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="effects-form-error" id="segments-error"></div>
            </div>
        `;
    }

    // 处理表单提交
    async handleFormSubmit() {
        const formData = this.getFormData();

        if (!this.validateForm(formData)) {
            return;
        }

        try {
            let response;
            if (this.currentEditingId) {
                // 更新灯效
                response = await fetch(`/api/effects/${this.currentEditingId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            } else {
                // 创建灯效
                response = await fetch('/api/effects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
            }

            const data = await response.json();

            if (data.success) {
                const isUpdate = !!this.currentEditingId; // 保存编辑状态
                this.showSuccess(data.message);
                this.hideModal();
                this.currentEditingId = null; // 重置编辑状态
                await this.loadEffects();
                this.renderEffects();

                // 发送灯效变化事件通知其他模块
                document.dispatchEvent(new CustomEvent('effectChanged', {
                    detail: {
                        action: isUpdate ? 'updated' : 'added',
                        timestamp: Date.now()
                    }
                }));
            } else {
                this.showError(data.message);
            }
        } catch (error) {
            console.error('提交灯效失败:', error);
            this.showError('提交灯效失败: ' + error.message);
        }
    }

    // 获取表单数据
    getFormData() {
        const name = document.getElementById('effect-name').value.trim();
        const effectType = parseInt(document.getElementById('effect-type').value);

        // 获取选中的分段
        const segments = [];
        document.querySelectorAll('#segments-selection .segment-button.selected').forEach(button => {
            const segmentId = button.dataset.segmentId;
            const activeDirectionBtn = button.querySelector('.direction-btn.active');
            const direction = activeDirectionBtn ? parseInt(activeDirectionBtn.dataset.direction) : 0;

            segments.push({
                segmentId: segmentId,
                direction: direction
            });
        });

        // 获取参数
        const params = {};

        // 亮度
        const brightnessSlider = document.getElementById('param-0');
        if (brightnessSlider) {
            params.brightness = parseInt(brightnessSlider.value);
        }

        // 速度
        const speedSlider = document.getElementById('param-1');
        if (speedSlider) {
            params.speed = parseInt(speedSlider.value);
        }

        // 颜色 - 支持多颜色
        const colors = this.getMultiColorData();
        if (colors.length > 0) {
            params.colors = colors;
        } else {
            // 设置默认红色
            params.colors = [{ r: 255, g: 0, b: 0 }];
        }

        // 强度
        const intensitySlider = document.getElementById('param-4');
        if (intensitySlider) {
            params.intensity = parseInt(intensitySlider.value);
        }

        return {
            name: name,
            effectType: effectType,
            segments: segments,
            params: params,
            enabled: true
        };
    }

    // 获取多颜色数据
    getMultiColorData() {
        const colorList = document.getElementById('color-list');
        if (!colorList) return [];

        const colors = [];
        const colorItems = colorList.querySelectorAll('.color-item');

        colorItems.forEach(item => {
            const picker = item.querySelector('.color-picker');
            if (picker) {
                const hex = picker.value;
                const r = parseInt(hex.substr(1, 2), 16);
                const g = parseInt(hex.substr(3, 2), 16);
                const b = parseInt(hex.substr(5, 2), 16);
                colors.push({ r, g, b });
            }
        });

        return colors;
    }

    // 验证表单
    validateForm(formData) {
        this.clearFormErrors();
        let isValid = true;

        // 验证名称
        if (!formData.name) {
            this.showFieldError('effect-name-error', '请输入预设名称');
            isValid = false;
        }

        // 验证灯效类型
        if (formData.effectType === '' || isNaN(formData.effectType)) {
            this.showFieldError('effect-type-error', '请选择灯效类型');
            isValid = false;
        }

        // 验证分段
        if (formData.segments.length === 0) {
            this.showFieldError('segments-error', '请至少选择一个分段');
            isValid = false;
        }

        return isValid;
    }

    // 显示字段错误
    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(fieldId);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    // 刷新分段数据
    async refreshSegments() {
        await this.loadAvailableSegments();

        // 如果模态框正在显示，立即更新参数容器（包含分段选项）
        const modal = document.getElementById('effect-modal');
        if (modal && modal.classList.contains('show')) {
            this.updateParamsContainer();
        }
    }

    // 清除表单错误
    clearFormErrors() {
        document.querySelectorAll('.effects-form-error').forEach(element => {
            element.style.display = 'none';
            element.textContent = '';
        });
    }

    // =================================================================
    // 预览功能实现
    // =================================================================

    // 绑定预览相关事件
    bindPreviewEvents() {
        this.previewToggle = document.getElementById('preview-mode-toggle');
        if (this.previewToggle) {
            this.previewToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.enablePreviewMode();
                } else {
                    this.disablePreviewMode();
                }
            });
        }
    }

    // 创建预览模式指示器
    createPreviewIndicator() {
        this.previewIndicator = document.createElement('div');
        this.previewIndicator.className = 'preview-mode-indicator';
        this.previewIndicator.textContent = '🎨 实时预览模式';
        document.body.appendChild(this.previewIndicator);
    }

    // 启用预览模式
    async enablePreviewMode() {
        try {
            const response = await fetch('/api/effects/preview/enable', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const data = await response.json();

            if (data.success) {
                this.previewMode = true;
                this.updatePreviewModeUI(true);

                if (window.AppUtils && window.AppUtils.showSuccess) {
                    window.AppUtils.showSuccess('预览模式已启用');
                }
            } else {
                this.previewToggle.checked = false; // 重置开关状态

                if (window.AppUtils && window.AppUtils.showError) {
                    window.AppUtils.showError('启用预览模式失败: ' + data.message);
                }
            }
        } catch (error) {
            this.previewToggle.checked = false; // 重置开关状态

            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('启用预览模式出错: ' + error.message);
            }
        }
    }

    // 禁用预览模式
    async disablePreviewMode() {
        try {
            const response = await fetch('/api/effects/preview/disable', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const data = await response.json();

            if (data.success) {
                this.previewMode = false;
                this.updatePreviewModeUI(false);

                if (window.AppUtils && window.AppUtils.showSuccess) {
                    window.AppUtils.showSuccess('预览模式已禁用，场景自动化已恢复');
                }
            } else {
                this.previewToggle.checked = true; // 重置开关状态

                if (window.AppUtils && window.AppUtils.showError) {
                    window.AppUtils.showError('禁用预览模式失败: ' + data.message);
                }
            }
        } catch (error) {
            this.previewToggle.checked = true; // 重置开关状态

            if (window.AppUtils && window.AppUtils.showError) {
                window.AppUtils.showError('禁用预览模式出错: ' + error.message);
            }
        }
    }

    // 更新预览模式UI状态
    updatePreviewModeUI(enabled) {
        if (this.previewIndicator) {
            if (enabled) {
                this.previewIndicator.classList.add('active');
            } else {
                this.previewIndicator.classList.remove('active');
            }
        }

        // 更新模块卡片的视觉状态
        const effectsModule = document.getElementById('effects-module');
        if (effectsModule) {
            if (enabled) {
                effectsModule.classList.add('preview-mode');
            } else {
                effectsModule.classList.remove('preview-mode');
            }
        }
    }

    // 触发实时预览（防抖处理）
    triggerPreview() {
        if (!this.previewMode) {
            return; // 不在预览模式，忽略
        }

        // 清除之前的定时器
        if (this.previewTimeout) {
            clearTimeout(this.previewTimeout);
        }

        // 设置防抖延迟
        this.previewTimeout = setTimeout(() => {
            this.sendPreviewRequest();
        }, 200); // 200ms防抖延迟
    }

    // 发送预览请求
    async sendPreviewRequest() {
        if (!this.previewMode) {
            return;
        }

        try {
            // 收集当前编辑的参数
            const previewData = this.collectCurrentParams();

            if (!previewData) {
                // 发送停止预览请求
                await this.sendStopPreviewRequest();
                return;
            }

            await fetch('/api/effects/preview', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(previewData)
            });

            // 预览请求发送完成，错误会在后端处理
        } catch (error) {
            // 网络错误等异常情况
        }
    }

    // 收集当前编辑的参数用于预览
    collectCurrentParams() {
        const modal = document.getElementById('effect-modal');

        if (!modal || !modal.classList.contains('show')) {
            return null; // 模态框未打开
        }

        // 收集表单数据
        const name = document.getElementById('effect-name')?.value || 'Preview';
        const effectType = parseInt(document.getElementById('effect-type')?.value);

        // 如果没有选择灯效类型，使用默认的呼吸灯效
        const finalEffectType = isNaN(effectType) ? 0 : effectType; // 0 = BREATHING

        // 收集选中的分段
        const segments = [];
        const selectedSegmentButtons = document.querySelectorAll('#segments-selection .segment-button.selected');

        selectedSegmentButtons.forEach(button => {
            const segmentId = button.getAttribute('data-segment-id');
            const activeDirectionBtn = button.querySelector('.direction-btn.active');
            const direction = activeDirectionBtn ? parseInt(activeDirectionBtn.getAttribute('data-direction')) : 0;

            segments.push({
                segmentId: segmentId,
                direction: direction
            });
        });

        // 如果没有选择分段，返回null（要求用户至少选择一个分段）
        if (segments.length === 0) {
            return null;
        }

        // 收集参数
        const params = {
            brightness: parseInt(document.getElementById('param-0')?.value) || 128,  // BRIGHTNESS = 0
            speed: parseInt(document.getElementById('param-1')?.value) || 50,        // SPEED = 1
            direction: parseInt(document.getElementById('param-3')?.value) || 0,     // DIRECTION = 3
            intensity: parseInt(document.getElementById('param-4')?.value) || 80,    // INTENSITY = 4
            colors: []
        };

        // 收集颜色参数
        const colorInputs = document.querySelectorAll('#color-list .color-picker');
        colorInputs.forEach(input => {
            const hex = input.value;
            const r = parseInt(hex.substr(1, 2), 16);
            const g = parseInt(hex.substr(3, 2), 16);
            const b = parseInt(hex.substr(5, 2), 16);
            params.colors.push({ r, g, b });
        });

        // 如果没有颜色，添加默认红色
        if (params.colors.length === 0) {
            params.colors.push({ r: 255, g: 0, b: 0 });
        }

        return {
            name: name,
            effectType: finalEffectType,
            segments: segments,
            params: params
        };
    }

    // 发送停止预览请求
    async sendStopPreviewRequest() {
        try {
            await fetch('/api/effects/preview', {
                method: 'DELETE'
            });
        } catch (error) {
            // 网络错误等异常情况，静默处理
        }
    }

    // 生成单套参数控件（从原有逻辑抽取出来）
    generateSingleParams(effectInfo, container) {
        let paramsHtml = '';

        // 定义参数显示顺序：分段 -> 颜色 -> 亮度 -> 速度 -> 强度
        const paramOrder = [5, 2, 0, 1, 4]; // SEGMENTS, COLOR, BRIGHTNESS, SPEED, INTENSITY

        paramOrder.forEach(paramType => {
            const param = effectInfo.availableParams.find(p => p.type === paramType);
            if (!param) return; // 如果该灯效没有这个参数，跳过
            if (param.type === 3) return; // 方向参数在分段选择中处理

            switch (param.type) {
                case 0: // BRIGHTNESS
                case 1: // SPEED
                case 4: // INTENSITY
                    paramsHtml += this.createSliderParam(param);
                    break;
                case 2: // COLOR
                    paramsHtml += this.createColorParam(param);
                    break;
                case 5: // SEGMENTS
                    paramsHtml += this.createSegmentsSelection();
                    break;
            }
        });

        container.innerHTML = paramsHtml;

        // 清理旧的事件监听器，然后绑定新的参数事件
        this.bindParamEvents(container);
    }
}

// 全局实例
let effectsManager;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    effectsManager = new EffectsManager();
});
